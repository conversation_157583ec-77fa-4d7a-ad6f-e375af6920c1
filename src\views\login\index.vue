<template>
  <div class="login-container">
    <!--    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="on" label-position="left">-->

    <!--      <div class="title-container">-->
    <!--        <h3 class="title">基药云商家管理后台</h3>-->
    <!--      </div>-->

    <!--      <el-form-item prop="username">-->
    <!--        <span class="svg-container">-->
    <!--          <svg-icon icon-class="user" />-->
    <!--        </span>-->
    <!--        <el-input-->
    <!--          ref="username"-->
    <!--          v-model="loginForm.username"-->
    <!--          placeholder="Username"-->
    <!--          name="username"-->
    <!--          type="text"-->
    <!--          tabindex="1"-->
    <!--          autocomplete="on"-->
    <!--        />-->
    <!--      </el-form-item>-->

    <!--      <el-tooltip v-model="capsTooltip" content="Caps lock is On" placement="right" manual>-->
    <!--        <el-form-item prop="password">-->
    <!--          <span class="svg-container">-->
    <!--            <svg-icon icon-class="password" />-->
    <!--          </span>-->
    <!--          <el-input-->
    <!--            :key="passwordType"-->
    <!--            ref="password"-->
    <!--            v-model="loginForm.password"-->
    <!--            :type="passwordType"-->
    <!--            placeholder="Password"-->
    <!--            name="password"-->
    <!--            tabindex="2"-->
    <!--            autocomplete="on"-->
    <!--            @keyup.native="checkCapslock"-->
    <!--            @blur="capsTooltip = false"-->
    <!--            @keyup.enter.native="handleLogin"-->
    <!--          />-->
    <!--          <span class="show-pwd" @click="showPwd">-->
    <!--            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />-->
    <!--          </span>-->
    <!--        </el-form-item>-->
    <!--      </el-tooltip>-->

    <!--      <el-button :loading="loading" type="primary" style="width:100%;margin-bottom:30px;" @click.native.prevent="handleLogin">登录</el-button>-->

    <!--      &lt;!&ndash; <div style="position:relative">-->
    <!--        <div class="tips">-->
    <!--          <span>Username : admin</span>-->
    <!--          <span>Password : any</span>-->
    <!--        </div>-->
    <!--        <div class="tips">-->
    <!--          <span style="margin-right:18px;">Username : editor</span>-->
    <!--          <span>Password : any</span>-->
    <!--        </div>-->

    <!--        <el-button class="thirdparty-button" type="primary" @click="showDialog=true">-->
    <!--          Or connect with-->
    <!--        </el-button>-->
    <!--      </div> &ndash;&gt;-->
    <!--    </el-form>-->

    <!--    <el-dialog title="Or connect with" :visible.sync="showDialog">-->
    <!--      Can not be simulated on local, so please combine you own business simulation! ! !-->
    <!--      <br>-->
    <!--      <br>-->
    <!--      <br>-->
    <!--      <social-sign />-->
    <!--    </el-dialog>-->
    <div class="login-container">
      <!-- <div class="login-header">
        <img :src="logoUrl" />
      </div> -->
      <div class="login-content">
        <div class="lt">
          <h2>密码登录</h2>
          <el-form
            ref="loginForm"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            autocomplete="off"
            label-position="left"
          >
            <el-form-item prop="username">
              <el-input
                ref="username"
                v-model="loginForm.username"
                placeholder="请输入登录账号"
                name="username"
                type="text"
                tabindex="1"
                autocomplete="off"
              />
            </el-form-item>
            <el-tooltip
              v-model="capsTooltip"
              content="Caps lock is On"
              placement="right"
              manual
            >
              <el-form-item prop="password">
                <el-input
                  :key="passwordType"
                  ref="password"
                  v-model="loginForm.password"
                  :type="passwordType"
                  placeholder="请输入账号登录密码"
                  name="password"
                  tabindex="2"
                  autocomplete="off"
                  @keyup.native="checkCapslock"
                  @blur="capsTooltip = false"
                  @keyup.enter.native="handleLogin"
                />
                <span class="show-pwd" @click="showPwd">
                  <svg-icon
                    :icon-class="
                      passwordType === 'password' ? 'eye' : 'eye-open'
                    "
                  />
                </span>
              </el-form-item>
            </el-tooltip>
            <el-form-item prop="key" v-if="isKey">
              <div style="display: flex">
                <el-input
                  v-model="loginForm.key"
                  placeholder="请输入短信验证码"
                  @keyup.enter.native="handleLogin"
                ></el-input>
                <el-button
                  @click="countDown"
                  :class="[
                    isDisable
                      ? 'ph_verification_disable'
                      : 'ph_verification_btn',
                    'verification_wd',
                  ]"
                >
                  {{ countDownText }}
                </el-button>
              </div>
            </el-form-item>
          </el-form>
          <div class="automatic">
            <el-checkbox v-model="automatic"></el-checkbox>1天内自动登录
          </div>
          <el-button
            :loading="loading"
            type="primary"
            class="submit"
            @click.native.prevent="handleLogin"
            >登录</el-button
          >
        </div>
        <div class="rt" v-if="!isKz && false">
          <span>他们都在用基药云</span>
          <p>打造医药数字营销闭环，与药企共赢医药价值</p>
          <img src="@/assets/imgs/business-list.png" />
        </div>
      </div>
      <div class="login-footer" v-if="!isKz">
        <span v-if="false"
          >Copyright @ 2020 基药云版权所有 All Right Reserved</span
        >
        备案号
        <a target="_blank" href="https://beian.miit.gov.cn"
          >粤ICP备20056016号-2</a
        ><br />
        客服邮箱：<EMAIL>
      </div>
    </div>
  </div>
</template>

<script>
import { isKz, logo } from "@/settings";
import {
  sendPhoneValidateCode,
  secondaryVerificationEnabled,
} from "@/api/user";

export default {
  name: "Login",
  data() {
    const validateUsername = (rule, value, callback) => {
      if (value == "" || value === null) {
        callback(new Error("账号不能为空"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (value === null || value === "") {
        callback(new Error("密码不能为空"));
      } else if (value.length < 6) {
        callback(new Error("密码长度不能小于六位"));
      } else {
        callback();
      }
    };
    return {
      countDownText: "获取验证码",
      isDisable: false,
      isKey: false,
      isKz,
      loginForm: {
        username: "",
        password: "",
        key: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", validator: validateUsername },
        ],
        password: [
          { required: true, trigger: "blur", validator: validatePassword },
        ],
      },
      passwordType: "password",
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
      automatic: false,
    };
  },
  computed: {
    logoUrl() {
      return require("@/assets/imgs/" + (logo || "login-logo.png"));
    },
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query;
        if (query) {
          this.redirect = query.redirect;
          this.otherQuery = this.getOtherQuery(query);
        }
      },
      immediate: true,
    },
  },
  created() {
    // window.addEventListener('storage', this.afterQRScan)
    secondaryVerificationEnabled().then((res) => {
      this.isKey = res.data;
    });
  },
  mounted() {
    if (this.loginForm.username === "") {
      this.$refs.username.focus();
    } else if (this.loginForm.password === "") {
      this.$refs.password.focus();
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    //验证码倒计时
    countDown() {
      if (!this.loginForm.username) return;
      if (this.isDisable) return;
      sendPhoneValidateCode({ account: this.loginForm.username }).then(
        (res) => {
          if (res.code == 0) {
            this.isDisable = true;
            let time = 60;
            let interval = setInterval(() => {
              time--;
              this.countDownText = `重新获取${time}s`;
              if (time == 0) {
                clearInterval(interval);
                this.isDisable = false;
                this.countDownText = "获取验证码";
              }
            }, 1000);
            this.$message({
              message: "验证码已发送，请在手机短信查看",
              type: "success",
            });
          }
        }
      );
    },
    checkCapslock(e) {
      const { key } = e;
      this.capsTooltip = key && key.length === 1 && key >= "A" && key <= "Z";
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$store
            .dispatch("user/login", this.loginForm)
            .then(() => {
              this.$router.push({
                path: this.redirect || "/",
                query: this.otherQuery,
              });
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== "redirect") {
          acc[cur] = query[cur];
        }
        return acc;
      }, {});
    },
    // afterQRScan() {
    //   if (e.key === 'x-admin-oauth-code') {
    //     const code = getQueryObject(e.newValue)
    //     const codeMap = {
    //       wechat: 'code',
    //       tencent: 'code'
    //     }
    //     const type = codeMap[this.auth_type]
    //     const codeName = code[type]
    //     if (codeName) {
    //       this.$store.dispatch('LoginByThirdparty', codeName).then(() => {
    //         this.$router.push({ path: this.redirect || '/' })
    //       })
    //     } else {
    //       alert('第三方登录失败')
    //     }
    //   }
    // }
  },
};
</script>

<style lang="scss">
.verification_wd {
  width: 100px;
  margin-left: 10px;
}

.ph_verification_btn {
  padding-left: 0;
  padding-right: 0;
  border: 1px solid #0056e5;
  color: #0056e5;
  background-color: rgba(0, 86, 229, 0.1);
}

.ph_verification_disable {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  color: #b4b9c1;
  border: 1px solid #e6e7ea;
}
.login-container {
  height: 100%;
  background-image: url("~@/assets/imgs/login-bg.png");
  background-size: 100% 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}
.login-header {
  position: fixed;
  top: 53px;
  left: 67px;
  width: 238px;
  height: 23px;
  img {
    // width: 238px;
    width: 100px;
    left: 23px;
  }
}
.login-footer {
  position: fixed;
  bottom: 52px;
  left: 0;
  right: 0;
  font-size: 13px;
  font-weight: 400;
  color: #999999;
  text-align: center;
  line-height: 24px;
}
.login-content {
  display: flex;
  margin-bottom: 100px;
  & > div {
    background-color: #fff;
    box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    height: 474px;
    overflow: hidden;
  }
  .lt {
    width: 506px;
    padding: 57px 47px 0;
    h2 {
      font-size: 28px;
      font-weight: 400;
      color: #333333;
      display: block;
      margin-bottom: 58px;
    }
    .el-form-item {
      margin-bottom: 28px;
      input {
        border-top: 0;
        border-left: 0;
        border-right: 0;
      }
    }
  }
  .rt {
    margin-left: 30px;
    text-align: center;
    padding: 49px 24px 24px;

    span {
      font-size: 28px;
      font-weight: 400;
      color: #333333;
    }
    p {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }
    img {
      margin-top: 21px;
      width: 307px;
      max-width: 307px;
      height: 312px;
      max-height: 312px;
    }
  }
}
.show-pwd {
  position: absolute;
  right: 10px;
  top: 2px;
  font-size: 20px;
  color: #889aa4;
  cursor: pointer;
  user-select: none;
}
@media only screen and (max-width: 470px) {
  .thirdparty-button {
    display: none;
  }
}
.login-form {
  margin: 0 13px;
}
.submit {
  width: 387px;
  height: 50px;
  margin: 32px 13px 0;
  font-size: 20px;
}
.automatic {
  margin-top: 57px;
  margin-left: 13px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  .el-checkbox {
    margin-right: 12px;
    .el-checkbox__input {
      display: inline-flex;
    }
  }
}
</style>
