import store from '@/store'
import { getToken, getRefreshToken, getExpire } from '@/utils/auth'
import { MessageBox } from 'element-ui'

// 请求队列管理
let isRefreshing = false // 是否正在刷新token
let failedQueue = [] // 挂起的请求队列

// 处理队列中的请求
const processQueue = (error, token = null) => {
  console.log(failedQueue, error, '====================failedQueue');

  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error)
    } else {
      prom.resolve(token)
    }
  })

  failedQueue = []
}

// 检查token是否即将过期（提前5分钟刷新）
const isTokenExpiringSoon = () => {
  const expire = getExpire()
  if (!expire) return false
  const currentTime = new Date().getTime()
  const fiveMinutes = 1 * 60 * 1000 // 5分钟

  return (expire - currentTime) < fiveMinutes
}

// 刷新token
const refreshToken = () => {
  return store.dispatch('user/reloadToken')
}

// 处理token过期的响应拦截器
const handleTokenExpired = (error, axiosInstance) => {
  const tokenExpiredCodes = [400, 40001, 50012, 50014, 40005, 40007, 40000, 40003]
  const isTokenExpiredError = error.response &&
    (error.response.status === 401 ||
      (error.response.data && tokenExpiredCodes.includes(error.response.data.code)))

  if (!isTokenExpiredError) {
    return Promise.reject(error)
  }

  // 如果正在刷新token，将请求加入队列
  if (isRefreshing) {
    return new Promise((resolve, reject) => {
      failedQueue.push({ resolve, reject })
    }).then(token => {
      error.config.headers['token'] = token
      return axiosInstance(error.config)
    }).catch(err => {
      return Promise.reject(err)
    })
  }

  // 开始刷新token
  isRefreshing = true
  const refreshTokenValue = `Bearer ${getRefreshToken()}`

  if (refreshTokenValue) {
    return refreshToken().then(() => {
      isRefreshing = false
      processQueue(null, `Bearer ${getToken()}`)
      // 重新发送原请求
      error.config.headers['token'] = `Bearer ${getToken()}`
      return axiosInstance(error.config)
    }).catch(refreshError => {
      isRefreshing = false
      processQueue(refreshError, null)
      // 刷新失败，提示用户重新登录
      MessageBox.confirm('登录状态已失效，请重新登录', {
        confirmButtonText: '前往登录',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      })
      return Promise.reject(refreshError)
    })
  } else {
    // 没有refreshToken，直接提示登录
    isRefreshing = false
    MessageBox.confirm('登录状态已失效，请重新登录', {
      confirmButtonText: '前往登录',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      store.dispatch('user/resetToken').then(() => {
        location.reload()
      })
    })
    return Promise.reject(new Error('No refresh token available'))
  }
}

// 处理响应数据中的token过期
const handleResponseTokenExpired = (response, axiosInstance) => {
  const res = response.data
  const tokenExpiredCodes = [400, 40001, 50012, 50014, 40005, 40007, 40000, 40003]

  if (res.code !== 0 && tokenExpiredCodes.includes(res.code)) {
    // 如果正在刷新token，将请求加入队列
    if (isRefreshing) {
      return new Promise((resolve, reject) => {
        failedQueue.push({ resolve, reject })
      }).then(token => {
        response.config.headers['token'] = token
        return axiosInstance(response.config)
      }).catch(err => {
        return Promise.reject(err)
      })
    }

    // 开始刷新token
    isRefreshing = true
    const refreshTokenValue = `Bearer ${getRefreshToken()}`

    if (refreshTokenValue) {
      return refreshToken().then(() => {
        isRefreshing = false
        processQueue(null, `Bearer ${getToken()}`)
        // 重新发送原请求
        response.config.headers['token'] = `Bearer ${getToken()}`
        return axiosInstance(response.config)
      }).catch(error => {
        isRefreshing = false
        processQueue(error, null)
        // 刷新失败，提示用户重新登录
        MessageBox.confirm('登录状态已失效，请重新登录', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
        return Promise.reject(error)
      })
    } else {
      // 没有refreshToken，直接提示登录
      isRefreshing = false
      MessageBox.confirm('登录状态已失效，请重新登录', {
        confirmButtonText: '前往登录',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      })
      return Promise.reject(new Error('No refresh token available'))
    }
  }

  return response
}

// 请求拦截器中检查token过期
const checkTokenInRequest = async () => {
  // 检查是否有token
  if (store.getters.token) {
    // 检查token是否即将过期
    if (isTokenExpiringSoon() && !isRefreshing) {
      isRefreshing = true
      try {
        await refreshToken()
        isRefreshing = false
        processQueue(null, `Bearer ${getToken()}`)
      } catch (error) {
        isRefreshing = false
        processQueue(error, null)
        // 刷新失败，跳转到登录页
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
        throw error
      }
    }
  }
}

export {
  isRefreshing,
  failedQueue,
  processQueue,
  isTokenExpiringSoon,
  refreshToken,
  handleTokenExpired,
  handleResponseTokenExpired,
  checkTokenInRequest
}
